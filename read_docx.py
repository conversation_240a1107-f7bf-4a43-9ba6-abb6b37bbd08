from docx import Document
import sys

try:
    doc = Document('支付结算年中工作汇报-2025.docx')
    print('=== 文档内容 ===')
    for i, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        if text:
            print(f'{i+1:3d}: {text}')
    
    print('\n=== 表格内容 ===')
    for table_idx, table in enumerate(doc.tables):
        print(f'表格 {table_idx + 1}:')
        for row_idx, row in enumerate(table.rows):
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip().replace('\n', ' ')
                if cell_text:
                    row_text.append(cell_text)
            if row_text:
                separator = ' | '
                print(f'  行 {row_idx + 1}: {separator.join(row_text)}')
        print()

except Exception as e:
    print(f'Error: {e}')
    import traceback
    traceback.print_exc()
